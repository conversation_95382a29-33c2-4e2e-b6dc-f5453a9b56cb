<!-- templates/translator_bot/translation_tool.html -->

{% extends "base.html" %}

{% block head_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<style>
    body, html, .translation-container, .card, .form-control, .btn {
        font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
    }
    
    #toggleAllColumns {
        transition: all 0.3s ease;
        border: 2px solid var(--warm-grey-secondary, #BFBAB0);
        color: var(--corporate-primary, #1E1F41);
        background-color: #ffffff;
        border-radius: 8px;
        font-weight: 500;
        padding: 8px 16px;
        font-size: 0.9rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        min-width: 130px;
    }

    #toggleAllColumns:hover {
        background-color: var(--warm-grey-light, #DFD5D2);
        border-color: var(--corporate-secondary, #7B869C);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    #toggleAllColumns:active {
        transform: translateY(0px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    #toggleAllColumns i {
        transition: transform 0.2s ease;
        font-size: 1rem;
    }

    #toggleAllColumns:hover i {
        transform: scale(1.1);
    }

    .excel-options-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .excel-options-header h5 {
        margin: 0;
        flex-grow: 1;
    }

    /* Professional Progress Bar Styles */
    .progress-custom {
        height: 16px;
        border-radius: 12px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: none;
        box-shadow: 
            inset 0 2px 4px rgba(0, 0, 0, 0.1),
            0 1px 2px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        position: relative;
    }

    .progress-custom::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, 
            rgba(255, 255, 255, 0.1) 0%, 
            rgba(255, 255, 255, 0.3) 50%, 
            rgba(255, 255, 255, 0.1) 100%);
        pointer-events: none;
    }

    .progress-bar-custom {
        background: linear-gradient(135deg, 
            var(--corporate-primary, #1E1F41) 0%,
            #4a5568 25%,
            #2d3748 50%,
            var(--corporate-primary, #1E1F41) 100%);
        border-radius: 10px;
        transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        box-shadow: 
            0 2px 8px rgba(30, 31, 65, 0.3),
            inset 0 1px 2px rgba(255, 255, 255, 0.2);
    }

    .progress-bar-custom::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 50%;
        background: linear-gradient(180deg, 
            rgba(255, 255, 255, 0.25) 0%, 
            rgba(255, 255, 255, 0.05) 100%);
        border-radius: 10px 10px 0 0;
    }

    .status-text {
        font-size: 0.95rem;
        color: #4a5568;
        margin-top: 0.75rem;
        font-weight: 600;
        text-align: left;
        letter-spacing: 0.025em;
    }

    /* Progress container enhancements */
    .progress-container {
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        margin-top: 1.5rem;
    }

    .progress-container .d-flex {
        margin-bottom: 0.75rem;
    }

    .progress-container .fw-semibold {
        font-size: 1rem;
        color: var(--corporate-primary, #1E1F41);
        font-weight: 600;
    }

    #progressPercent {
        font-size: 1rem;
        font-weight: 700;
        color: var(--corporate-primary, #1E1F41);
        background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
        padding: 0.25rem 0.75rem;
        border-radius: 6px;
        border: 1px solid #e2e8f0;
        min-width: 60px;
        text-align: center;
    }

    /* Loading Spinner Styles */
    .loading-spinner {
        display: none;
        text-align: center;
        padding: 20px;
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        margin-top: 15px;
    }

    .spinner {
        width: 30px;
        height: 30px;
        border: 3px solid #e9ecef;
        border-top: 3px solid var(--corporate-primary, #1E1F41);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 10px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .loading-text {
        color: #6c757d;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .download-link-custom {
        background: var(--corporate-primary, #1E1F41);
        color: #fff;
        border: none;
        border-radius: 8px;
        font-size: 1.15rem;
        font-weight: 600;
        padding: 0.85rem 2.5rem;
        box-shadow: 0 2px 8px rgba(30, 31, 65, 0.10);
        transition: background 0.2s, transform 0.15s, box-shadow 0.15s;
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        margin: 0 auto;
        letter-spacing: 0.01em;
    }
    .download-link-custom:hover, .download-link-custom:focus {
        background: #23244a;
        color: #fff;
        transform: translateY(-2px) scale(1.03);
        box-shadow: 0 4px 16px rgba(30, 31, 65, 0.16);
        text-decoration: none;
    }
    .download-link-custom i {
        font-size: 1.3rem;
    }
    .download-link-wrapper {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="translation-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10 col-xl-8">
                <div class="translation-card">
                    <div class="card-header-custom position-relative">
                        <span class="header-icon-topright">
                            <i class="fas fa-language"></i>
                        </span>
                        <h1 class="mb-0" style="font-size: 2.5rem; font-weight: 700;">Document Translation Tool</h1>
                        <p class="mb-0 mt-2 opacity-75">
                            Translate Excel documents with AI
                        </p>
                    </div>
                    
                    <div class="card-body p-4">
                        <!-- File Upload Section -->
                        <div class="mb-4">
                            <h5 class="mb-3">
                                <i class="fas fa-upload me-2"></i>
                                Upload Document
                            </h5>
                            
                            <div class="upload-area" id="uploadArea">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <h6>Drag & drop your file here</h6>
                                <p class="text-muted mb-3">or click to browse</p>
                                <p class="small text-muted">
                                    Supported formats: Excel (.xlsx), PowerPoint (.pptx), Word (.docx)
                                    <br>Maximum file size: 50MB
                                </p>
                                <input type="file" id="fileInput" accept=".xlsx,.pptx,.docx" style="display: none;">
                            </div>
                            
                            <div id="fileInfo" class="file-info" style="display: none;">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-file me-3 text-success"></i>
                                    <div class="flex-grow-1">
                                        <div class="fw-bold" id="fileName"></div>
                                        <div class="small text-muted" id="fileDetails"></div>
                                    </div>
                                    <button class="btn btn-sm btn-outline-danger" id="removeFile">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Loading Spinner -->
                            <div id="loadingSpinner" class="loading-spinner">
                                <div class="spinner"></div>
                                <div class="loading-text" id="loadingText">Uploading and processing file...</div>
                            </div>
                        </div>

                        <!-- Excel Column Selection (hidden by default) -->
                        <div id="excelOptions" class="excel-options" style="display: none;">
                            <div class="excel-options-header">
                                <h5>
                                    <i class="fas fa-table me-2"></i>
                                    Excel Column Selection
                                </h5>
                                <button type="button" class="btn btn-sm btn-outline-secondary" id="toggleAllColumns">
                                    <i class="fas fa-check-square me-1"></i>
                                    Select All
                                </button>
                            </div>
                            <p class="text-muted mb-3">Select which columns you want to translate:</p>

                            <div class="row" id="columnCheckboxes">
                                <!-- Checkboxes will be populated dynamically -->
                            </div>
                        </div>

                        <!-- Language Selection -->
                        <div class="mt-4" id="languageSettings" style="display: none;">
                            <h5 class="mb-3">
                                <i class="fas fa-globe me-2"></i>
                                Language Settings
                            </h5>
                            
                            <div class="language-grid">
                                <div>
                                    <label for="sourceLanguage" class="form-label fw-semibold">Source Language</label>
                                    <select class="form-select form-select-custom" id="sourceLanguage">
                                        <option value="auto">Auto-detect</option>
                                        <option value="en">English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                        <option value="it">Italian</option>
                                        <option value="pt">Portuguese</option>
                                        <option value="ru">Russian</option>
                                        <option value="ja">Japanese</option>
                                        <option value="ko">Korean</option>
                                        <option value="zh">Chinese</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label for="targetLanguage" class="form-label fw-semibold">Target Language</label>
                                    <select class="form-select form-select-custom" id="targetLanguage">
                                        <option value="">Select target language</option>
                                        <option value="en">English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                        <option value="it">Italian</option>
                                        <option value="pt">Portuguese</option>
                                        <option value="ru">Russian</option>
                                        <option value="ja">Japanese</option>
                                        <option value="ko">Korean</option>
                                        <option value="zh">Chinese</option>
                                        <option value="ar">Arabic</option>
                                        <option value="hi">Hindi</option>
                                        <option value="th">Thai</option>
                                        <option value="vi">Vietnamese</option>
                                        <option value="nl">Dutch</option>
                                        <option value="sv">Swedish</option>
                                        <option value="no">Norwegian</option>
                                        <option value="da">Danish</option>
                                        <option value="fi">Finnish</option>
                                        <option value="pl">Polish</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Translation Button -->
                        <div class="text-center mt-4">
                            <button class="btn btn-translate btn-lg" id="translateBtn" disabled>
                                <i class="fas fa-magic me-2"></i>
                                Start Translation
                            </button>
                        </div>



                        <!-- Progress Section (hidden by default) -->
                        <div id="progressSection" class="progress-container" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="fw-semibold">Translation Progress</span>
                                <span id="progressPercent">0%</span>
                            </div>
                            <div class="progress progress-custom">
                                <div class="progress-bar progress-bar-custom" id="progressBar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="status-text" id="statusText">Initializing...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block tail_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileDetails = document.getElementById('fileDetails');
    const removeFile = document.getElementById('removeFile');
    const excelOptions = document.getElementById('excelOptions');
    const columnCheckboxes = document.getElementById('columnCheckboxes');
    const toggleAllColumns = document.getElementById('toggleAllColumns');
    const translateBtn = document.getElementById('translateBtn');
    const targetLanguage = document.getElementById('targetLanguage');
    const progressSection = document.getElementById('progressSection');
    const progressBar = document.getElementById('progressBar');
    const progressPercent = document.getElementById('progressPercent');
    const statusText = document.getElementById('statusText');
    const loadingSpinner = document.getElementById('loadingSpinner');
    const loadingText = document.getElementById('loadingText');
    const languageSettings = document.getElementById('languageSettings');
    
    let selectedFile = null;
    let currentProgress = 0;

    // Helper function to show alerts
    function showAlert(message, type = 'info') {
        // Create a simple alert div
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alertDiv);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }

    // Upload area click handler
    uploadArea.addEventListener('click', () => {
        fileInput.click();
    });

    // Drag and drop handlers
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    });

    // File input change handler
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleFileSelect(e.target.files[0]);
        }
    });

    // Remove file handler
    removeFile.addEventListener('click', () => {
        selectedFile = null;
        fileInfo.style.display = 'none';
        uploadArea.style.display = 'block';
        excelOptions.style.display = 'none';
        loadingSpinner.style.display = 'none';
        fileInput.value = '';
        languageSettings.style.display = 'none';
        updateTranslateButton();
    });

    // Target language change handler
    targetLanguage.addEventListener('change', updateTranslateButton);

    // Translate button handler
    translateBtn.addEventListener('click', startTranslation);

    // Toggle all columns button handler
    toggleAllColumns.addEventListener('click', function() {
        const checkboxes = columnCheckboxes.querySelectorAll('input[type="checkbox"]');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);

        checkboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
        });

        // Update button text and icon
        const icon = this.querySelector('i');
        const text = this.querySelector('span') || this.childNodes[this.childNodes.length - 1];

        if (allChecked) {
            icon.className = 'fas fa-square me-1';
            this.innerHTML = '<i class="fas fa-square me-1"></i>Select All';
        } else {
            icon.className = 'fas fa-check-square me-1';
            this.innerHTML = '<i class="fas fa-check-square me-1"></i>Deselect All';
        }

        updateTranslateButton();
    });

    function handleFileSelect(file) {
        // Validate file type
        const allowedTypes = ['.xlsx', '.pptx', '.docx'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        
        if (!allowedTypes.includes(fileExtension)) {
            showAlert('Please select a valid file type: Excel (.xlsx), PowerPoint (.pptx), or Word (.docx)', 'warning');
            return;
        }

        // Validate file size (50MB limit)
        if (file.size > 50 * 1024 * 1024) {
            showAlert('File size must be less than 50MB', 'warning');
            return;
        }

        selectedFile = file;

        languageSettings.style.display = 'block';
        
        // Show loading spinner
        uploadArea.style.display = 'none';
        loadingSpinner.style.display = 'block';
        loadingText.textContent = 'Uploading and processing file...';

        // Upload file and get column information for Excel files
        if (fileExtension === '.xlsx') {
            uploadFileToServer(file);
        } else {
            // For non-Excel files, simulate a brief loading time
            setTimeout(() => {
                loadingSpinner.style.display = 'none';
                
                // Update file info display
                fileName.textContent = file.name;
                fileDetails.textContent = `${(file.size / 1024 / 1024).toFixed(2)} MB • ${fileExtension.toUpperCase().substring(1)} file`;
                fileInfo.style.display = 'block';
                
                excelOptions.style.display = 'none';
                updateTranslateButton();
            }, 800);
        }
    }

    function uploadFileToServer(file) {
        const formData = new FormData();
        formData.append('file', file);
        
        loadingText.textContent = 'Uploading file to server...';

        fetch('/translator/api/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadingText.textContent = 'Getting Excel columns...';
                // For Excel files, get columns from the new endpoint
                if (file.name.toLowerCase().endsWith('.xlsx')) {
                    getExcelColumns();
                } else {
                    finishFileLoading();
                }
            } else {
                loadingSpinner.style.display = 'none';
                uploadArea.style.display = 'block';
                alert('Error uploading file: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Upload error:', error);
            loadingSpinner.style.display = 'none';
            uploadArea.style.display = 'block';
            alert('Error uploading file');
        });
    }

    function finishFileLoading() {
        // Hide loading spinner and show file info
        loadingSpinner.style.display = 'none';
        
        // Update file info display
        fileName.textContent = selectedFile.name;
        const fileExtension = '.' + selectedFile.name.split('.').pop().toLowerCase();
        
        fileInfo.style.display = 'block';
        updateTranslateButton();
    }

    function getExcelColumns() {
        loadingText.textContent = 'Analyzing Excel columns...';
        
        fetch('/translator/api/columns', {
            method: 'GET'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showExcelOptions(data.columns, null);
                finishFileLoading();
            } else {
                console.error('Error getting columns:', data.error);
                loadingSpinner.style.display = 'none';
                uploadArea.style.display = 'block';
                alert('Error analyzing Excel file: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Get columns error:', error);
            loadingSpinner.style.display = 'none';
            uploadArea.style.display = 'block';
            alert('Error analyzing Excel file');
        });
    }

    function showExcelOptions(columns, preview) {
        columnCheckboxes.innerHTML = '';
        
        // Show preview section
        if (preview && preview.length > 0) {
            const previewDiv = document.createElement('div');
            previewDiv.className = 'mb-3';
            previewDiv.innerHTML = `
                <h6>File Preview:</h6>
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>${columns.map(col => `<th>${col}</th>`).join('')}</tr>
                        </thead>
                        <tbody>
                            ${preview.slice(0, 3).map(row => 
                                `<tr>${columns.map(col => `<td>${row[col] || ''}</td>`).join('')}</tr>`
                            ).join('')}
                        </tbody>
                    </table>
                </div>
            `;
            columnCheckboxes.appendChild(previewDiv);
        }
        
        // Show column checkboxes
        const checkboxContainer = document.createElement('div');
        checkboxContainer.className = 'row';
        
        columns.forEach((column, index) => {
            const colDiv = document.createElement('div');
            colDiv.className = 'col-md-6 column-checkbox';
            
            colDiv.innerHTML = `
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="${column}" id="col${index}" checked>
                    <label class="form-check-label" for="col${index}">
                        ${column}
                    </label>
                </div>
            `;
            
            checkboxContainer.appendChild(colDiv);
        });
        
        columnCheckboxes.appendChild(checkboxContainer);

        // Initialize toggle button state (all checkboxes are checked by default)
        toggleAllColumns.innerHTML = '<i class="fas fa-check-square me-1"></i>Deselect All';

        excelOptions.style.display = 'block';
    }
    
    function showTranslationPreview() {
        if (!selectedFile || !targetLanguage.value) {
            alert('Please select a file and target language');
            return;
        }

        // Get selected columns
        const checkboxes = columnCheckboxes.querySelectorAll('input[type="checkbox"]:checked');
        const selectedColumns = Array.from(checkboxes).map(cb => cb.value);

        if (selectedColumns.length === 0) {
            alert('Please select at least one column to translate');
            return;
        }

        // For now, preview only the first selected column
        const columnToPreview = selectedColumns[0];

        // Call preview API with new format
        fetch('/translator/api/preview', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                column_name: columnToPreview,
                target_language: getLanguageName(targetLanguage.value)
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showPreviewModal(data.preview_html, data.total_rows, [data.column]);
            } else {
                alert('Preview failed: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Preview error:', error);
            alert('Preview failed');
        });
    }
    
    function showPreviewModal(previewHtml, totalRows, columns) {
        // Create modal for preview
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Translation Preview</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>Preview showing first 10 rows of ${totalRows} total rows</p>
                        ${previewHtml}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary" onclick="startTranslation()">Proceed with Full Translation</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
        
        // Remove modal from DOM when hidden
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }
    
    function getLanguageName(code) {
        const languages = {
            'en': 'English', 'es': 'Spanish', 'fr': 'French', 'de': 'German',
            'it': 'Italian', 'pt': 'Portuguese', 'ru': 'Russian', 'ja': 'Japanese',
            'ko': 'Korean', 'zh': 'Chinese', 'ar': 'Arabic', 'hi': 'Hindi',
            'th': 'Thai', 'vi': 'Vietnamese', 'nl': 'Dutch', 'sv': 'Swedish',
            'no': 'Norwegian', 'da': 'Danish', 'fi': 'Finnish', 'pl': 'Polish'
        };
        return languages[code] || code;
    }

    function updateTranslateButton() {
        const hasFile = selectedFile !== null;
        const hasTargetLanguage = targetLanguage.value !== '';
        
        translateBtn.disabled = !(hasFile && hasTargetLanguage);
    }

    // Enhanced progress bar update function
    function updateProgressBar(targetProgress, statusMessage = '') {
        const increment = 2; // Progress increment per frame
        const interval = 50; // Update interval in milliseconds
        
        const updateProgress = () => {
            if (currentProgress < targetProgress) {
                currentProgress = Math.min(currentProgress + increment, targetProgress);
                progressBar.style.width = currentProgress + '%';
                progressPercent.textContent = currentProgress + '%';
                
                if (statusMessage) {
                    statusText.textContent = statusMessage;
                }
                
                if (currentProgress < targetProgress) {
                    setTimeout(updateProgress, interval);
                }
            }
        };
        
        updateProgress();
    }

    // Simulate initial progress phases
    function simulateInitialProgress() {
        updateProgressBar(15, 'Initializing translation...');
        
        setTimeout(() => {
            updateProgressBar(30, 'Processing file...');
        }, 1000);
        
        setTimeout(() => {
            updateProgressBar(45, 'Analyzing content...');
        }, 2000);
        
        setTimeout(() => {
            updateProgressBar(60, 'Starting translation...');
        }, 3000);
    }

    function startTranslation() {
        if (!selectedFile || !targetLanguage.value) {
            alert('Please select a file and target language');
            return;
        }
        
        // Get selected columns for Excel files
        let selectedColumns = [];
        if (selectedFile.name.toLowerCase().endsWith('.xlsx')) {
            const checkboxes = columnCheckboxes.querySelectorAll('input[type="checkbox"]:checked');
            selectedColumns = Array.from(checkboxes).map(cb => cb.value);
        }
        
        progressSection.style.display = 'block';
        translateBtn.disabled = true;
        currentProgress = 0;
        
        // Start initial progress simulation
        simulateInitialProgress();
        
        // Start translation
        const translationData = {
            target_language: targetLanguage.value,
            source_language: document.getElementById('sourceLanguage').value,
            selected_columns: selectedColumns,
            file_type: '.' + selectedFile.name.split('.').pop().toLowerCase()
        };
        
        fetch('/translator/api/translate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(translationData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.status === 'completed') {
                    // Translation completed immediately (Excel files)
                    updateProgressBar(100, 'Translation complete!');
                    
                    // Show download button after progress completes
                    setTimeout(() => {
                        const downloadWrapper = document.createElement('div');
                        downloadWrapper.className = 'download-link-wrapper';
                        downloadWrapper.style.display = 'flex';
                        downloadWrapper.style.justifyContent = 'center';
                        downloadWrapper.style.alignItems = 'center';
                        downloadWrapper.style.marginTop = '2rem';

                        const downloadLink = document.createElement('a');
                        downloadLink.href = `/translator/api/download/${data.translation_id}`;
                        downloadLink.className = 'btn btn-success btn-lg download-link-custom';
                        downloadLink.innerHTML = '<i class="fas fa-download me-2"></i>Download Translated File';

                        downloadWrapper.appendChild(downloadLink);
                        progressSection.appendChild(downloadWrapper);

                        // Show preview if available
                        if (data.data) {
                            showPreview(data.data);
                        }
                    }, 1000);
                } else {
                    // Handle ongoing translation process
                    monitorTranslationProgress(data.translation_id);
                }
                translateBtn.disabled = false;
            } else {
                alert('Translation failed: ' + data.error);
                progressSection.style.display = 'none';
                translateBtn.disabled = false;
                currentProgress = 0;
            }
        })
        .catch(error => {
            console.error('Translation error:', error);
            alert('Translation failed');
            progressSection.style.display = 'none';
            translateBtn.disabled = false;
            currentProgress = 0;
        });
    }
    
    function showPreview(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            const previewDiv = document.createElement('div');
            previewDiv.className = 'mt-4';
            previewDiv.innerHTML = `
                <h6>Translation Preview (First 5 rows):</h6>
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>${Object.keys(data[0] || {}).map(key => `<th>${key}</th>`).join('')}</tr>
                        </thead>
                        <tbody>
                            ${data.slice(0, 5).map(row => 
                                `<tr>${Object.values(row).map(val => `<td>${val}</td>`).join('')}</tr>`
                            ).join('')}
                        </tbody>
                    </table>
                </div>
            `;
            progressSection.appendChild(previewDiv);
        } catch (e) {
            console.error('Error showing preview:', e);
        }
    }
    
    function monitorTranslationProgress(translationId) {
        // For non-Excel files that require polling
        const interval = setInterval(() => {
            fetch(`/translator/api/status/${translationId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Use smooth progress update instead of direct setting
                    const targetProgress = Math.max(data.progress, currentProgress);
                    updateProgressBar(targetProgress, data.status);
                    
                    if (data.status === 'completed') {
                        clearInterval(interval);
                        // Ensure we reach 100% before showing download
                        setTimeout(() => {
                            const downloadWrapper = document.createElement('div');
                            downloadWrapper.className = 'download-link-wrapper';
                            const downloadLink = document.createElement('a');
                            downloadLink.href = data.download_url;
                            downloadLink.className = 'btn btn-success btn-lg download-link-custom';
                            downloadLink.innerHTML = '<i class="fas fa-download me-2"></i>Download Translated File';
                            downloadWrapper.appendChild(downloadLink);
                            progressSection.appendChild(downloadWrapper);
                        }, 500);
                    }
                }
            })
            .catch(error => {
                console.error('Status check error:', error);
                clearInterval(interval);
                statusText.textContent = 'Error checking translation status';
            });
        }, 1500); // Reduced interval for more responsive updates
    }
});
</script>
{% endblock %}
